package org.springblade.modules.hzyc.DocumentGeneration.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import org.springblade.modules.hzyc.DocumentGeneration.service.DocumentGenerator;
import org.springblade.modules.hzyc.DocumentGeneration.service.ICaseInfoService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springblade.modules.hzyc.document.service.IDocumentService;
import org.springblade.modules.hzyc.document.pojo.entity.DocumentEntity;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 案件调查终结报告文档生成实现类
 *
 * <AUTHOR>
 */
@Service("investigationReportDocument")
public class CaseInvestigationReportImpl implements DocumentGenerator {

    @Autowired
    private IDocumentService documentService;
	@Autowired
	private ICaseInfoService icaseInfoService;

    @Override
    public List<Map<String, Object>> processData(Map<String, Object> rawData, String caseId, String mode, String fileId) {
        Map<String, Object> processedData = new HashMap<>(rawData);
        // 如果mode为update，从数据库读取已保存的数据
        if ("update".equals(mode) && caseId != null) {
            DocumentEntity existingDoc = documentService.getById(fileId);
            if (existingDoc != null && existingDoc.getDocumentContent() != null) {
                // 从JSON字符串解析为Map
                processedData = JsonUtil.readMap(existingDoc.getDocumentContent());
                return List.of(processedData);
            }
        }

        // 如果没有找到已保存的数据或mode不是update，使用模拟数据
		processedData = StrUtil.isNotBlank(caseId) ? getData(caseId) : getMockData();
        return List.of(processedData);
    }

    @Override
    public String getTemplateName() {
        return "23案件调查终结报告_个人.docx";//todo 区分个人/单位
    }

    @Override
    public String getDocumentType() {
        return "CASE-INVESTIGATION-REPORT";
    }

	private Map<String, Object> getData(String caseId) {
		Map<String, Object> data = new HashMap<>();
		Map<String, Object> query=new HashMap<>();
		query.put("AJUUID", caseId);
		JSONArray array = icaseInfoService.getCaseInvestigationEndDailyReport(query);
		if(array != null && !array.isEmpty()) {
			Map<String, Object> firstData = (Map<String, Object>) array.get(0);
			Map<String, String> mapper = getTableFieldMapping();
			if(firstData != null) {
				// 处理数据
				firstData.forEach((key, value) -> {
					String newKey = mapper.get(key);
					if (StrUtil.isBlank(newKey)) {
						newKey = key;
					}
					data.put(newKey, value);
				});
				System.out.println(data);
				return data;
			}
		}
		return data;
	}


	public static Map<String, String> getTableFieldMapping() {
		Map<String, String> fieldMapping = new HashMap<>();

		// 前端字段映射 - 使用驼峰命名法与前端保持一致
		fieldMapping.put("FDDBR", "picPerson");           // 当事人
		fieldMapping.put("AY", "causeOfAction");          // 案由
		fieldMapping.put("AJLY", "caseFrom");             // 案件来源
		fieldMapping.put("LARQ", "regDate");              // 立案日期
		fieldMapping.put("DSR", "party");                 // 姓名
		fieldMapping.put("XB", "sex");                    // 性别
		fieldMapping.put("NL", "age");                    // 年龄
		fieldMapping.put("MZ", "nation");                 // 民族
		fieldMapping.put("ZJLX", "idCardType");           // 证件类型
		fieldMapping.put("ZJHM", "idCard");               // 证件号码
		fieldMapping.put("LXDH", "phone");                // 联系电话
		fieldMapping.put("DZ", "address");                // 住址
		fieldMapping.put("CBR", "undertaker");            // 调查人
		fieldMapping.put("DCSS", "invstgContent");        // 调查事实
		fieldMapping.put("AJXZ", "caseProperty");         // 案件性质
		fieldMapping.put("CFYJ", "punishArgument");       // 处罚依据
		fieldMapping.put("CLYJ", "handleAdvice");         // 处理意见
		fieldMapping.put("BGRQ", "rptDate");              // 报告日期
		fieldMapping.put("BZ", "remarks");                // 备注

		// 保留其他系统字段的映射
		fieldMapping.put("SJMC", "city_org_name");
		fieldMapping.put("XGR", "modifier");
		fieldMapping.put("CBBMUUID", "reg_dept_uuid");
		fieldMapping.put("JGJC", "org_shortname");
		fieldMapping.put("TAR", "same_party");
		fieldMapping.put("XYWYBS", "tid");
		fieldMapping.put("JGSXZ", "org_abbr");
		fieldMapping.put("WCZT", "finsh_status");
		fieldMapping.put("ZZJGUUID", "org_uuid");
		fieldMapping.put("SJSSBMYYTYSJQXCL", "own_dept_uuid");
		fieldMapping.put("CHBMUUID", "get_dept_uuid");
		fieldMapping.put("XTGXSJCXBYDX", "sys_modify_time");
		fieldMapping.put("WFFLTK", "legal_argument");
		fieldMapping.put("AJBH", "case_code");
		fieldMapping.put("AYUUIDS", "prop_uuids");
		fieldMapping.put("SJSSDWYYTYSJQXCL", "own_org_uuid");
		fieldMapping.put("MCRKSJ", "mc_tec_ctime");
		fieldMapping.put("JGJC_", "org_short_name");
		fieldMapping.put("DCZJRQ", "invstg_end_date");
		fieldMapping.put("KZZD2", "ext2");
		fieldMapping.put("FWZXZDTBSJYFWZXSJS", "sysisdelete");
		fieldMapping.put("CJR", "creator");
		fieldMapping.put("XGSJ", "modify_time");
		fieldMapping.put("FWZXZDTBSJYFWZXSJG", "sysupdatetime");
		fieldMapping.put("CJSJ", "create_time");
		fieldMapping.put("SJBM", "city_org_code");
		fieldMapping.put("XTCJSJCXBYDX", "sys_create_time");
		fieldMapping.put("SFYX", "is_active");
		fieldMapping.put("CSRQ", "birthday");
		fieldMapping.put("AJXZID", "property_uuid");
		fieldMapping.put("KZZD3", "ext3");
		fieldMapping.put("AJMC", "case_name");
		fieldMapping.put("KZZD1", "ext1");
		fieldMapping.put("SFWZ", "is_no_master");
		fieldMapping.put("AJBS", "case_uuid");
		fieldMapping.put("ZJBGBS", "invstg_rpt_uuid");
		fieldMapping.put("CBRUUIDS", "undertaker_uuids");

		return fieldMapping;
	}

    /**
     * 获取模拟数据
     */
    private Map<String, Object> getMockData() {
        Map<String, Object> mockData = new HashMap<>();

        // 基础信息
        mockData.put("report_uuid", "51f8b0721d3c43fdb0adbc151c3a1489");
        mockData.put("case_uuid", "6358d676d24647f1b824c1f362674299");
        mockData.put("org_shortname", "广东省博罗县烟草专卖局");
        mockData.put("full_doc_no", "博烟调﹝2025﹞第48号");
        mockData.put("doc_date", "2025年6月10日");

        // 案件基本信息
        mockData.put("case_name", "梁俊强涉嫌违法经营卷烟案");
        mockData.put("case_code", "博烟案﹝2025﹞第48号");
        mockData.put("case_start_date", "2025年03月18日");
        mockData.put("case_end_date", "2025年06月10日");
        mockData.put("investigation_period", "84天");

        // 当事人信息
        mockData.put("party_name", "梁俊强");
        mockData.put("party_info", "当事人：梁俊强，字号：博罗县龙溪隆胜轩茶烟酒商行，性别：男性，民族：汉族，职业：个体工商户，身份证住址：广东省博罗县龙溪街道长湖村合湖小组193号，居民身份证号码：441322199203166034，烟草专卖零售许可证号：441322113031，统一社会信用代码：92441322MA56B9KR4D，经营地址：广东省博罗县龙溪街道宫庭村龙桥大道1239号，联系电话：13640736270。");

        // 案件来源
        mockData.put("case_source", "群众举报");
        mockData.put("case_source_detail", "2025年03月18日，我局接群众举报称位于广东省惠州市博罗县龙溪街道宫庭村龙桥大道1239号的\"博罗县龙溪隆胜轩茶烟酒商行\"有涉嫌违法经营卷烟的行为。");

        // 调查过程
        mockData.put("investigation_process", "接报后我局马上组织人员到现场进行检查。我局专卖执法人员叶辉明(19090352015),朱兆强(19090352023)经出示执法证件，表明身份，说明来意后，依法对位于广东省惠州市博罗县龙溪街道宫庭村龙桥大道1239号\"博罗县龙溪隆胜轩茶烟酒商行\"（烟草专卖零售许可证号：441322113031）的经营场所进行检查。\n\n检查过程中，在该场所内发现涉嫌违法的烟草专卖品黄果树(长征)200条、白沙(硬精品三代)150条、红塔山(硬经典)150条、黄山(新一品)100条、娇子(蓝时代)100条、白沙(硬)75条、天子(金)25条、金圣(硬滕王阁)25条、黄金叶(喜满堂)25条、双喜(硬经典1906)25条、金圣(庐山)25条、大前门(软)25条、黄山(印象一品)25条、黄鹤楼(硬金砂)25条、红旗渠(新版银河)25条、双喜(莲香)25条、芙蓉王(硬)50条，合计17个品种1075条。\n\n随后，执法人员依法对涉案卷烟进行先行登记保存，制作了现场笔录，对当事人进行了询问，并提取了相关证据材料。");

        // 违法事实认定
        mockData.put("violation_facts", "经调查认定，当事人梁俊强经营的博罗县龙溪隆胜轩茶烟酒商行存在未在当地烟草专卖批发企业进货的违法行为。具体表现为：\n\n1. 当事人承认其店内部分卷烟系从非正规渠道进货；\n2. 涉案卷烟共17个品牌规格合计1075条，价值108625.00元；\n3. 当事人无法提供从当地烟草专卖批发企业进货的相关凭证；\n4. 经检验鉴定，涉案卷烟均为真品。");

        // 证据清单
        List<Map<String, Object>> evidenceList = new ArrayList<>();

        Map<String, Object> evidence1 = new HashMap<>();
        evidence1.put("evidence_no", 1);
        evidence1.put("evidence_name", "涉案卷烟实物");
        evidence1.put("evidence_desc", "17个品牌规格合计1075条卷烟");
        evidence1.put("evidence_source", "现场检查发现");
        evidenceList.add(evidence1);

        Map<String, Object> evidence2 = new HashMap<>();
        evidence2.put("evidence_no", 2);
        evidence2.put("evidence_name", "证据先行登记保存通知书");
        evidence2.put("evidence_desc", "对涉案卷烟进行先行登记保存的法律文书");
        evidence2.put("evidence_source", "执法程序");
        evidenceList.add(evidence2);

        Map<String, Object> evidence3 = new HashMap<>();
        evidence3.put("evidence_no", 3);
        evidence3.put("evidence_name", "证据复制（提取）单");
        evidence3.put("evidence_desc", "提取当事人身份证、许可证复印件及现场照片等");
        evidence3.put("evidence_source", "证据提取");
        evidenceList.add(evidence3);

        Map<String, Object> evidence4 = new HashMap<>();
        evidence4.put("evidence_no", 4);
        evidence4.put("evidence_name", "询问笔录");
        evidence4.put("evidence_desc", "对当事人梁俊强的询问记录");
        evidence4.put("evidence_source", "询问调查");
        evidenceList.add(evidence4);

        Map<String, Object> evidence5 = new HashMap<>();
        evidence5.put("evidence_no", 5);
        evidence5.put("evidence_name", "现场笔录");
        evidence5.put("evidence_desc", "现场检查情况记录");
        evidence5.put("evidence_source", "现场检查");
        evidenceList.add(evidence5);

        Map<String, Object> evidence6 = new HashMap<>();
        evidence6.put("evidence_no", 6);
        evidence6.put("evidence_name", "卷烟、雪茄烟鉴别检验报告");
        evidence6.put("evidence_desc", "对涉案卷烟真伪的检验鉴定结果");
        evidence6.put("evidence_source", "技术鉴定");
        evidenceList.add(evidence6);

        Map<String, Object> evidence7 = new HashMap<>();
        evidence7.put("evidence_no", 7);
        evidence7.put("evidence_name", "涉案物品核价表");
        evidence7.put("evidence_desc", "对涉案卷烟价值的核定");
        evidence7.put("evidence_source", "价格认定");
        evidenceList.add(evidence7);

        mockData.put("evidence_list", evidenceList);

        // 法律适用
        mockData.put("legal_application", "当事人的上述行为违反了《中华人民共和国烟草专卖法实施条例》第二十三条第二款的规定，构成了未在当地烟草专卖批发企业进货的违法行为。根据《中华人民共和国烟草专卖法实施条例》第五十六条的规定，应当给予相应的行政处罚。");

        // 处理建议
        mockData.put("treatment_suggestion", "建议对当事人梁俊强作出如下处理：\n\n1. 没收违法所得；\n2. 处以进货总额人民币108625.00元的9.5％罚款，计罚款人民币10319.37元；\n3. 没收涉案卷烟1075条。\n\n处罚依据：《中华人民共和国烟草专卖法实施条例》第五十六条、《广东省烟草专卖行政处罚裁量权管理办法》第十一条第（三）项。");

        // 调查人员
        mockData.put("investigator1_name", "叶辉明");
        mockData.put("investigator1_insp_no", "19090352015");
        mockData.put("investigator2_name", "朱兆强");
        mockData.put("investigator2_insp_no", "19090352023");

        // 审核人员
        mockData.put("reviewer_name", "蔡秋宝");
        mockData.put("reviewer_position", "专卖科科长");

        // 系统字段
        mockData.put("is_active", 1);
        mockData.put("creator", "蔡秋宝");
        mockData.put("create_time", "2025/6/10 17:15");
        mockData.put("modifier", "蔡秋宝");
        mockData.put("modify_time", "2025/6/10 17:15");
        mockData.put("city_org_code", "10441300");
        mockData.put("city_org_name", "惠州市");

        return mockData;
    }
}
